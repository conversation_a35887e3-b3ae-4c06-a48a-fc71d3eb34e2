<template>
  <!-- 收信审核页面 -->
  <div class="review-wrap">
    <div class="bsp-base-form">
      <div class="bsp-base-tit">
        {{ editTitle }}
      </div>
      <div class="content-container">
        <!-- 直接引入detail组件 -->
        <div class="detail-section">
          <detail
            :jgrybm="jgrybm"
            :jgryxm="jgryxm"
            :roomName="roomName"
            :curId="curId"
            @close="() => {}"
          />
        </div>

        <!-- 底部审批表单卡片 -->
        <div class="approval-section">
          <div class="approval-card">
            <h3 class="approval-card-title">
              <i class="approval-card-icon">✅</i>
              审核信息
            </h3>
            <div class="approval-card-content">
              <Form v-if="saveType === 'approval'" ref="releaseForm" :model="approvalData" :label-width="150"
                    :label-colon="true" class="base-form-container">
                <Row>
                  <Col span="12" v-if="formData.status === '01'">
                    <FormItem label="审核结果" prop="result"
                              :rules="[{ trigger: 'blur,change', message: '请选择', required: saveType === 'approval',}]">
                      <RadioGroup v-model="approvalData.result">
                        <Radio label="5" :disabled="saveType !== 'approval'">通过</Radio>
                        <Radio label="2" :disabled="saveType !== 'approval'">另行处理</Radio>
                      </RadioGroup>
                    </FormItem>
                  </Col>
                  <Col span="12" v-if="formData.status !== '01'">
                    <FormItem label="审核结果" prop="result"
                              :rules="[{ trigger: 'blur,change', message: '请选择', required: saveType === 'approval',}]">
                      <RadioGroup v-model="approvalData.result">
                        <Radio label="5" :disabled="saveType !== 'approval'">同意</Radio>
                        <Radio label="2" :disabled="saveType !== 'approval'">不通过</Radio>
                      </RadioGroup>
                    </FormItem>
                  </Col>
                  <Col span="12" v-if="formData.status === '01'">
                    <FormItem label="另行处理情况登记">
                      <Input type="textarea" :disabled="saveType !== 'approval'" :autosize="{minRows: 3,maxRows: 5}"
                             v-model="approvalData.approvalComments" placeholder="请填写另行处理情况登记" maxlength="500"/>
                    </FormItem>
                  </Col>
                  <Col span="12" v-if="formData.status !== '01'">
                    <FormItem label="审批意见">
                      <Input type="textarea" :disabled="saveType !== 'approval'" :autosize="{minRows: 3,maxRows: 5}"
                             v-model="approvalData.approvalComments" placeholder="请填写审批意见" maxlength="500"/>
                    </FormItem>
                  </Col>
                </Row>
              </Form>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="bsp-base-fotter">
      <Button style="margin: 0 20px" @click="handleClose()">返 回</Button>
      <Button style="margin: 0 20px" type="primary" v-if="saveType === 'approval'" :loading="loading"
              @click="handleSubmit()">保 存
      </Button>
    </div>
  </div>
</template>

<script>
import detail from './detail.vue'
export default {
  components: {
    detail
  },
  props: {
    curId: {
      default: '',
      type: String
    },
    jgrybm: {
      default: '',
      type: String
    },
    jgryxm: {
      default: '',
      type: String
    },
    saveType: {
      default: 'add',
      type: String
    },
    roomName: {
      default: '',
      type: String
    }
  },
  data() {
    return {
      editTitle: '收信审核',
      openModal: false,
      loading: false,
      formData: {},
      approvalData: {
        approvalComments: '',
        id: this.curId
      }
    }
  },
  methods: {
    handleSubmit() {
      if (!this.approvalData.result) {
        this.$Message.error('请选择审核结果')
        return
      }
      if (this.approvalData.result === '2' && !this.approvalData.approvalComments) {
        if (this.formData.status === '01') {
          this.$Message.error('请填写另行处理情况登记')
        } else {
          this.$Message.error('请填写审批意见')
        }
        return
      }
      this.loading = true
      this.$store.dispatch("authPostRequest", {
        url: this.$path.familyContact_approval,
        params: this.approvalData
      }).then(res => {
        if (res.success) {
          this.loading = false
          this.handleClose()
          this.$Message.success('操作成功')
        } else {
          this.$Message.error(res.msg || '保存失败！')
          this.loading = false
        }
      })
    },
    handleClose() {
      this.$emit('close', false)
    },
    getData() {
      this.$store.dispatch("authGetRequest", {
        url: this.$path.familyContact_get,
        params: {
          id: this.curId
        }
      }).then(res => {
        if (res.success) {
          this.formData = res.data
          this.formData.jgryxm = this.jgryxm
          this.formData.roomName = this.roomName
        } else {
          this.$Modal.error({
            title: '温馨提示',
            content: res.msg || '操作失败'
          })
        }
      })
    }
  },
  mounted() {
    this.getData()
  }
}
</script>

<style scoped lang="less">
@import '~@/assets/style/variables.less';

.review-wrap {
  height: 100%;
  display: flex;
  flex-direction: column;

  .bsp-base-form {
    position: relative !important;
    top: auto !important;
    left: auto !important;
    right: auto !important;
    bottom: auto !important;
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
  }

  .bsp-base-tit {
    flex-shrink: 0;
  }

  .bsp-base-fotter {
    position: relative !important;
    left: auto !important;
    right: auto !important;
    bottom: auto !important;
    flex-shrink: 0;
    margin-top: auto;
  }
}

.content-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  gap: @margin-sm;
  padding: @padding-md;
  background-color: @background-color-light;
  border-radius: @border-radius-lg;
}

.detail-section {
  flex: 1;
  overflow: hidden;

  /* 隐藏detail组件的标题和底部按钮 */
  /deep/ .bsp-base-tit {
    display: none;
  }

  /deep/ .bsp-base-fotter {
    display: none;
  }

  /* 调整detail组件的布局 */
  /deep/ .detail-wrap {
    height: 100%;

    .bsp-base-form {
      height: 100%;
    }
  }
}

.approval-section {
  flex-shrink: 0;
  height: 200px;
}

.approval-card {
  background: @background-color-base;
  border-radius: @border-radius-lg;
  box-shadow: @box-shadow-light;
  border: 1px solid @border-color-light;
  transition: all 0.3s ease;
  height: 100%;
  display: flex;
  flex-direction: column;

  &:hover {
    box-shadow: @box-shadow-medium;
  }

  .approval-card-title {
    background: linear-gradient(135deg, #52c41a 0%, #389e0d 100%);
    color: @background-color-base;
    margin: 0;
    padding: 12px @padding-md;
    font-size: 14px;
    font-weight: 600;
    display: flex;
    align-items: center;
    height: 44px;
    box-sizing: border-box;
    border-radius: @border-radius-lg @border-radius-lg 0 0;

    .approval-card-icon {
      margin-right: 12px;
      font-size: @font-size-md;
    }
  }

  .approval-card-content {
    padding: @padding-md;
    flex: 1;
    overflow-y: auto;

    .base-form-container {
      .ivu-form-item {
        margin-bottom: @margin-sm;
      }

      .ivu-input {
        border-radius: @border-radius-sm;
      }

      .ivu-radio-group {
        .ivu-radio-wrapper {
          margin-right: @margin-md;
        }
      }
    }
  }
}

/* 响应式设计 */
@media (max-width: @screen-md) {
  .approval-section {
    height: auto;
    min-height: 200px;
  }

  .approval-card-content {
    .base-form-container {
      .ivu-col {
        width: 100% !important;
      }
    }
  }
}
</style>
