<template>
  <!-- 收信审核页面 -->
  <div class="review-wrap">
    <div class="bsp-base-form">
      <div class="bsp-base-tit">
        {{ editTitle }}
      </div>
      <div class="prison-select-center">
        <!-- 引入detail页面的三列布局 -->
        <div class="three-column-layout">
          <!-- 左列：被监管人员信息 -->
          <div class="left-column">
            <div class="personnel-card">
              <h3 class="personnel-card-title">
                <i class="personnel-card-icon">👤</i>
                被监管人员
              </h3>
              <div class="personnel-card-content">
                <personnel-selector
                  :value="jgrybm"
                  mode="detail"
                  title="被监管人员"
                  :show-case-info="true"
                />
              </div>
            </div>
          </div>

          <!-- 中列：基本信息 -->
          <div class="middle-column">
            <headerDetail :formData="formData" :jgryxm="jgryxm" :roomName="roomName"/>
          </div>

          <!-- 右列：流程轨迹 -->
          <div class="right-column">
            <div class="timeline-container">
              <h3 class="timeline-title">
                <i class="timeline-title-icon">📋</i>
                流程轨迹
              </h3>
              <div class="timeline-wrapper">
                <record :formData="formData" />
              </div>
            </div>
          </div>
        </div>

        <!-- 底部审批表单卡片 -->
        <div class="approval-section">
          <div class="approval-card">
            <h3 class="approval-card-title">
              <i class="approval-card-icon">✅</i>
              审核信息
            </h3>
            <div class="approval-card-content">
              <Form v-if="saveType === 'approval'" ref="releaseForm" :model="approvalData" :label-width="150"
                    :label-colon="true" class="base-form-container">
                <Row>
                  <Col span="12" v-if="formData.status === '01'">
                    <FormItem label="审核结果" prop="result"
                              :rules="[{ trigger: 'blur,change', message: '请选择', required: saveType === 'approval',}]">
                      <RadioGroup v-model="approvalData.result">
                        <Radio label="5" :disabled="saveType !== 'approval'">通过</Radio>
                        <Radio label="2" :disabled="saveType !== 'approval'">另行处理</Radio>
                      </RadioGroup>
                    </FormItem>
                  </Col>
                  <Col span="12" v-if="formData.status !== '01'">
                    <FormItem label="审核结果" prop="result"
                              :rules="[{ trigger: 'blur,change', message: '请选择', required: saveType === 'approval',}]">
                      <RadioGroup v-model="approvalData.result">
                        <Radio label="5" :disabled="saveType !== 'approval'">同意</Radio>
                        <Radio label="2" :disabled="saveType !== 'approval'">不通过</Radio>
                      </RadioGroup>
                    </FormItem>
                  </Col>
                  <Col span="12" v-if="formData.status === '01'">
                    <FormItem label="另行处理情况登记">
                      <Input type="textarea" :disabled="saveType !== 'approval'" :autosize="{minRows: 3,maxRows: 5}"
                             v-model="approvalData.approvalComments" placeholder="请填写另行处理情况登记" maxlength="500"/>
                    </FormItem>
                  </Col>
                  <Col span="12" v-if="formData.status !== '01'">
                    <FormItem label="审批意见">
                      <Input type="textarea" :disabled="saveType !== 'approval'" :autosize="{minRows: 3,maxRows: 5}"
                             v-model="approvalData.approvalComments" placeholder="请填写审批意见" maxlength="500"/>
                    </FormItem>
                  </Col>
                </Row>
              </Form>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="bsp-base-fotter">
      <Button style="margin: 0 20px" @click="handleClose()">返 回</Button>
      <Button style="margin: 0 20px" type="primary" v-if="saveType === 'approval'" :loading="loading"
              @click="handleSubmit()">保 存
      </Button>
    </div>
  </div>
</template>

<script>
import headerDetail from './headerDetail.vue'
import {sGeneralHistory} from 'sd-general-history'
import ryxx from "./ryxx.vue";
import record from "./record.vue";
export default {
  components: {
    record,
    ryxx,
    headerDetail,
    sGeneralHistory
  },
  props: {
    curId: {
      default: '',
      type: String
    },
    jgrybm: {
      default: '',
      type: String
    },
    jgryxm: {
      default: '',
      type: String
    },
    saveType: {
      default: 'add',
      type: String
    },
    roomName: {
      default: '',
      type: String
    }
  },
  data() {
    return {
      editTitle: '收信审核',
      openModal: false,
      loading: false,
      formData: {},
      approvalData: {
        approvalComments: '',
        id: this.curId
      }
    }
  },
  methods: {
    handleSubmit() {
      if (!this.approvalData.result) {
        this.$Message.error('请选择审核结果')
        return
      }
      if (this.approvalData.result === '2' && !this.approvalData.approvalComments) {
        if (this.formData.status === '01') {
          this.$Message.error('请填写另行处理情况登记')
        } else {
          this.$Message.error('请填写审批意见')
        }
        return
      }
      this.loading = true
      this.$store.dispatch("authPostRequest", {
        url: this.$path.familyContact_approval,
        params: this.approvalData
      }).then(res => {
        if (res.success) {
          this.loading = false
          this.handleClose()
          this.$Message.success('操作成功')
        } else {
          this.$Message.error(res.msg || '保存失败！')
          this.loading = false
        }
      })
    },
    handleClose() {
      this.$emit('close', false)
    },
    getData() {
      this.$store.dispatch("authGetRequest", {
        url: this.$path.familyContact_get,
        params: {
          id: this.curId
        }
      }).then(res => {
        if (res.success) {
          this.formData = res.data
          this.formData.jgryxm = this.jgryxm
          this.formData.roomName = this.roomName
        } else {
          this.$Modal.error({
            title: '温馨提示',
            content: res.msg || '操作失败'
          })
        }
      })
    }
  },
  mounted() {
    this.getData()
  }
}
</script>

<style scoped lang="less">

.prison-select-center-bottom-left {
  width: 40%;
  background: #fff;
  height: 100%;
}

.prison-select-center-bottom-right {
  width: 60%;
  background: #fff;
  margin-left: 5px;
  height: 100%;
}



.prison-select-center {
  background-color: #e9eef5;
  border-radius: 6px;
  width: 100%;
  display: flex;
  flex-direction: column;
  height: 100%;
}

.prison-select-center-top {
  width: 100%;
  height: 45%;
  border-radius: 6px;
  overflow: hidden;
  background: #fff;
  display: flex;
  margin-bottom: 5px;
  .prison-select-center-top-ryxx {
    width: 400px;
    padding: 44px 25px 25px 35px;
  }
  .prison-select-center-top-detail {
    width: calc(~"100% - 400px");
  }
}

.prison-select-center-bottom {
  display: flex;
  flex: 1;
  min-height: 0;
  height: 100%;
}
</style>
